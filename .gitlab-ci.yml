variables:
  SERVICE_CODE: "yanxuan-dhxy-workbench"
  ARTIFACT_PATH: $CI_PROJECT_NAME.zip # 云外打包文件名称
  ARTIFACT_YUN_PATH: ${CI_PROJECT_NAME}-yun.zip # 云上打包文件名称

stages:
  - install
  - package
  - deploy

before_script:
  - preCheck

install-deps:
  image: $CI_IMAGE_FRONT_NODE_14
  stage: install
  script:
    - pwd
    - sh scripts/install.sh
  tags:
    - ci-front
  cache:
    key: "$CI_PROJECT_NAME"
    # install 之后更新缓存
    policy: push
    paths:
      - node_modules/
      - web/node_modules/
      - server/node_modules/
  only:
    refs:
      - /^(dev|release|hotfix|feature).*$/
    changes:
      # 只在 package-lock.json 有变化时才执行该 stage
      - package-lock.json
      - web/package-lock.json
      - server/package-lock.json

# # 目前feature做lint校验
# package-lint:
#   image: $CI_IMAGE_FRONT_NODE_14
#   stage: package
#   script:
#     - npm run eslint
#     - npm run eslint-stat -- --service=$SERVICE_CODE --uid=$GITLAB_USER_EMAIL --branch=$CI_COMMIT_REF_NAME
#   cache:
#     policy: pull
#     key: "$CI_PROJECT_NAME"
#     paths:
#       - node_modules/
#       - web/node_modules/
#   tags:
#     - ci-front
#   only: # 只允许操作的分支
#     - /^feature.*$/
#   dependencies:
#     - install-deps

.build_package: &build_definition
  image: $CI_IMAGE_FRONT_NODE_14
  stage: package
  script:
    - pwd # 绝对路径
    - npm run build:$PACKAGE_ENV # build，看代码是否报错
    - cp deploy/env/setenv_$PACKAGE_ENV.sh dist/setenv.sh # 拷贝deploy/env/setenv_test.sh 到 dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./* # 在dist下压缩云外资源
    # - zip -rq ../$ARTIFACT_YUN_PATH ./* # 打包云上镜像
  artifacts: # 用于指定成功后应附加到job的文件和目录的列表 存附件
    paths:
      - $ARTIFACT_PATH
      # - $ARTIFACT_YUN_PATH
    expire_in: 3d # 有效期
  cache:
    policy: pull
    key: "$CI_PROJECT_NAME"
    paths:
      - web/node_modules/
      - server/node_modules/
  tags:
    - ci-front

.deploy_artifacts_package: &deploy_artifacts_definition
  stage: deploy
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=$PACKAGE_ENV --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=$MODULE  --autoDeploy=true --clusterId=0
  tags:
    - ci-front

# 测试编译
package-fed-test:
  <<: *build_definition
  variables:
    PACKAGE_ENV: test
  only: # 只允许操作的分支
    - master
    - /^hotfix-.*$/
    - /^feature.+$/
    - /^release-.*$/
  dependencies:
    - install-deps

################################
#    编译打包(regression)
################################
package-fed-regression:
  <<: *build_definition
  variables:
    PACKAGE_ENV: regression
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - install-deps

# 线上编译
package-fed-online:
  <<: *build_definition
  variables:
    PACKAGE_ENV: online
  only:
    - master
    - /^(release|hotfix).*$/
  dependencies:
    - install-deps

# bate-yun编译
# package-fed-betayun:
#   <<: *build_definition
#   variables:
#     PACKAGE_ENV: betayun
#   only:
#     - feature-betayun
#   dependencies:
#     - install-deps

# upload_artifacts-betayun:
#   <<: *deploy_artifacts_definition
#   variables:
#     PACKAGE_ENV: dev
#     MODULE: dev
#   tags:
#     - betayun-runner
#   only:
#     - /^(release|hotfix|dev|feature).*$/
#   dependencies:
#     - package-fed-betayun
#   when: manual

# bate-yun上传代码制品
# image_upload_betayun:
#   stage: deploy
#   script:
#     - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
#     - version_tools version && PROJECT_VERSION=$(version_tools result)
#     - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
#     - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/${SERVICE_CODE}:${IMAGE_TAG}"
#     - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
#     - docker build --pull -t ${IMAGE_NAME} -f betayun-Dockerfile  .
#     - eval opera docker $OPERA_ARGS --env=dev --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG} --autoDeploy=true --module=dev
#   tags:
#     - betayun-runner
#   only:
#     - feature-betayun
#   dependencies:
#     - package-fed-betayun

# 测试服上传代码制品
upload_artifacts-test:
  <<: *deploy_artifacts_definition
  variables:
    PACKAGE_ENV: test
    MODULE: test
  only: # 只允许操作的分支
    - dev
    - /^hotfix-.*$/
    - /^release-.*$/
    - /^feature.+$/
    - master
  dependencies:
    - package-fed-test

# 云镜像上传
# upload_image_test:
#   stage: deploy
#   script:
#     - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
#     - version_tools version && PROJECT_VERSION=$(version_tools result)
#     - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
#     - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/${SERVICE_CODE}:${IMAGE_TAG}"
#     - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
#     # 强制删除target并重新新建一个target文件夹，解压云内打包文件到target文件夹下，将target/setenv.sh移到当前目录下
#     - docker build --pull -t ${IMAGE_NAME} -f Dockerfile .
#     # 使用 Dockerfile 创建镜像，标签为IMAGE_NAME，并尝试去更新镜像的新版本
#     - eval opera docker $OPERA_ARGS --env=test --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG} --module=test --autoDeploy=true --clusterId=44 --ldcCode=cluster1
#     # 将制品上传至opera，并自动部署
#   tags:
#     - ci-front
#   when: manual
#   only:
#     - dev
#     - /^hotfix-.*$/
#     - /^release-.*$/
#     - /^feature.+$/
#   dependencies:
#     - package-fed-test

#  回归环境
upload_artifacts-regression:
  <<: *deploy_artifacts_definition
  variables:
    PACKAGE_ENV: regression
    MODULE: default
  when: manual
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-regression

# 回归环境上传镜像
# upload_image_regression:
#   stage: deploy
#   script:
#     - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
#     - version_tools version && PROJECT_VERSION=$(version_tools result)
#     - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
#     - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/${SERVICE_CODE}:${IMAGE_TAG}"
#     - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
#     - docker build --pull -t ${IMAGE_NAME}  .
#     - eval opera docker $OPERA_ARGS --env=regression --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG}  --module=default  --autoDeploy=true --clusterId=186 --ldcCode=ldc-hz-jd-reg
#   tags:
#     - ci-front
#   when: manual
#   only:
#     - /^(release|hotfix).*$/
#   dependencies:
#     - package-fed-regression

# 线上环境上传代码制品
upload_artifacts-online:
  stage: deploy
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=default
  tags:
    - ci-front
  when: manual
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-online
# 线上环境上传镜像
# upload_image_online:
#   stage: deploy
#   script:
#     - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
#     - version_tools version && PROJECT_VERSION=$(version_tools result)
#     - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
#     - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/${SERVICE_CODE}:${IMAGE_TAG}"
#     - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
#     - docker build --pull -t ${IMAGE_NAME}  .
#     - eval opera docker $OPERA_ARGS --env=online --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG}  --module=default
#   tags:
#     - ci-front
#   when: manual
#   only:
#     - /^(release|hotfix).*$/
#   dependencies:
#     - package-fed-online
