#!/bin/bash
set -o errexit

# 编译前端
npm run web:build:test
# 编译后端
npm run server:build:test

rm -rf dist
mkdir dist

# 拷贝 opera 部署需要的环境变量
# cp deploy/env/setenv_test.sh dist/setenv.sh

# 创建前端要拷贝到后端的文件夹
mkdir -p dist/web            
mkdir -p dist/mimg

application="web/build/application"
module="web/build/module"

if [ -d "$application" ]; then
# 拷贝前端的结果，到后端文件夹                                
cp -rf web/build/application/app dist/web                
# 拷贝为了在下一步cdn的时候用                                
cp -rf web/build/application/mimg/* dist/mimg            
fi                                                       

if [ -d "$module" ]; then                                
# 拷贝前端的结果，到后端文件夹                                
cp -rf web/build/module/app dist/web                     
# 拷贝为了在下一步cdn的时候用                                
cp -rf web/build/module/mimg/* dist/mimg                 
fi                                                       


# 拷贝server打包后的代码
cp -rf server/dist/* dist/
# 拷贝对应的package.json到server/dist目录
cp server/package.json dist
cp server/package-lock.json dist
# PM2的文件
cp server/process.json dist
# 只安装生产环境需要的依赖
mv server/node_modules dist/node_modules