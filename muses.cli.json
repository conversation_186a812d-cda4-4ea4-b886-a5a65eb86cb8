{"domain": "b", "packageManager": "yarn", "additionalCmd": {"bin": "npm"}, "projects": {"dhxy-workbench": {"root": "web", "entry": [{"html": "public/index.html", "indexJs": "src/index"}], "env": {"base": {"CONTEXT_PATH": "/dhxy", "PRODUCT_CODE": "act", "SERVICE_CODE": "yanxuan-dhxy-workbench", "SKIP_PREFLIGHT_CHECK": true}, "dev": {"PROJECT_TYPE": "application", "PUBLIC_URL": "./", "HOST": "local.yx.mail.netease.com", "PORT": 9000, "REMOTE_URL": "http://local.yx.mail.netease.com:8080", "MOCK_PATH": "mock", "XHR_PREFIX": "/xhr", "DISABLE_ESLINT_PLUGIN": false, "IGNORE_TYPE_CHECK": true, "INLINE_RUNTIME_CHUNK": false, "BUNDLE_ANAlYZER": false}, "test": {"PROJECT_TYPE": "application", "PUBLIC_URL": "//nos.netease.com/mailpub-test/hxm/yanxuan-dhxy-workbench/", "GENERATE_SOURCEMAP": true}, "regression": {"PROJECT_TYPE": "application", "PUBLIC_URL": "//nos.netease.com/mailpub-test/hxm/yanxuan-dhxy-workbench/", "GENERATE_SOURCEMAP": true}, "betayun": {"PROJECT_TYPE": "application", "PUBLIC_URL": "//nos.netease.com/mailpub-test/hxm/yanxuan-dhxy-workbench/", "GENERATE_SOURCEMAP": true}, "online": {"PROJECT_TYPE": "application", "PUBLIC_URL": "//mailpub.nosdn.127.net/hxm/yanxuan-dhxy-workbench/", "GENERATE_SOURCEMAP": false}}}}}