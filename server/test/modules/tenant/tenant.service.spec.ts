import { Service, Test } from '@tiger/boot';
import { expect } from 'chai';
import { TenantService } from '../../../src/modules/xhr/tenant/tenant.service';

@Service
class TenantServiceTest {
    constructor(private tenantService: TenantService) { }

    /**
     * 测试getYxCategorys的函数
     * @author: 金炳
     * @date: 2019-03-27 12:08:49
     */
    @Test
    async getYxCategorys() {
        expect(
            JSON.stringify(await this.tenantService.getTenantList())
        ).to.be.equal(
            JSON.stringify([
                {
                    id: 100,
                    name: '租户1',
                    description: '租户x1'
                },
                {
                    id: 101,
                    name: '租户2',
                    description: '租户x2'
                },
                {
                    id: 100,
                    name: '租户3',
                    description: '租户x3'
                },
                {
                    id: 100,
                    name: '租户4',
                    description: '租户x4'
                },
            ])
        );
    }

    /**
     * 测试访问TenantItem的函数
     * @author: 金炳
     * @date: 2019-03-27 12:10:33
     */
    @Test
    async getTenantItem() {
        const result = {
            id: 100,
            name: '租户4',
            description: '租户x4'
        }
        expect(
            JSON.stringify(await this.tenantService.getTenantItem())
        ).to.be.equal(JSON.stringify(result));
    }
}
