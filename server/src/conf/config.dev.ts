import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
    loggerPath: string = join(this.rootPath, 'log');
    appProxyOptions: ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path;
        }
    };
    umcProxyOptions: ITigerProxyOption = {
        target: 'http://************',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'yxius.you.163.com' },
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
                ),
                ''
            );
        }
    };
}
