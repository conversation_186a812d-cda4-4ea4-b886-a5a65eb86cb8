import { TenantItemPO } from './po/tenant-item.po';
import { Service } from '@tiger/boot';

@Service
export class TenantService {
    async getTenantList(): Promise<TenantItemPO[]> {
        const list: TenantItemPO[] = [
            {
                id: 100,
                name: '租户1',
                description: '租户x1'
            },
            {
                id: 101,
                name: '租户2',
                description: '租户x2'
            },
            {
                id: 100,
                name: '租户3',
                description: '租户x3'
            },
            {
                id: 100,
                name: '租户4',
                description: '租户x4'
            },
        ];
        return list;
    }
    async getTenantItem(): Promise<TenantItemPO> {
        const item: TenantItemPO =
        {
            id: 100,
            name: '租户4',
            description: '租户x4'
        };
        return item;
    }
}
