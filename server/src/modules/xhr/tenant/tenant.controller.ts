import {
    GetMapping,
    PostMapping,
    RequestMapping,
    RestController
} from '@tiger/boot';
import { AjaxResult, QueryContext, RequestContext } from '@tiger/core';
import { valid } from '@tiger/validator';
import { TenantService } from './tenant.service';
import { ParamsVO } from './vo/params.vo';
import { TenantItemPO } from './po/tenant-item.po'

/**
 * 租户查询 
 * @data: 2018-10-28 19:21:52
 */
@RestController
@RequestMapping(`/tenant`, [])
export class TenantController {
    constructor(private tenantService: TenantService) { }

    /** 查询租户列表 */
    @GetMapping('/list.json', [])
    async getTenantList(
        ctx: RequestContext<null, AjaxResult<{ result: TenantItemPO[], pagination: { page: number, size: number, total: number, totalPage: number } }>>
    ) {
        const tenantList = await this.tenantService.getTenantList();
        ctx.body = AjaxResult.success({ result: tenantList, pagination: { page: 1, size: 10, total: 100, totalPage: 10 } });
    }
    
    /** 查询租户详情 */
    @GetMapping('/detail.json', [])
    async getTenantItem(
        ctx: RequestContext<null, AjaxResult<TenantItemPO>>
    ) {
        const tenantItem = await this.tenantService.getTenantItem();
        ctx.body = AjaxResult.success(tenantItem);
    }

    /**
     * 验证参数校验
     * @author: 金炳
     * @data: 2018-12-28 10:30:24
     */
    @GetMapping('/params.do', [valid(ParamsVO)])
    async params(ctx: QueryContext<ParamsVO, AjaxResult<any>>) {
        ctx.body = AjaxResult.success('hello world');
    }
}
