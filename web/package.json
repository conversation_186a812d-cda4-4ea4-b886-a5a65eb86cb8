{"name": "dhxy-workbench", "version": "0.0.1", "private": true, "dependencies": {"@babel/eslint-parser": "^7.28.0", "@eagler/authorizion": "^2.0.0", "@shark/core": "^1.1.3", "@sharkr/components": "^2.6.9", "@sharkr/css": "^2.0.0", "@sharkr/request": "^1.1.0", "antd": "4.19.5", "react": "16.12.0", "react-css-modules": "^4.7.11", "react-dom": "16.12.0", "react-router": "5.1.2", "react-router-dom": "5.1.2"}, "scripts": {"dev": "cross-env MUSE_ENV=dev muses-scripts dev", "build": "cross-env MUSE_ENV=test muses-scripts build", "build:test": "cross-env MUSE_ENV=test muses-scripts build", "build:regression": "cross-env MUSE_ENV=regression muses-scripts build", "build:betayun": "cross-env MUSE_ENV=betayun muses-scripts build", "build:online": "cross-env MUSE_ENV=online muses-scripts build", "eslint": "eslint --ext .js,.jsx,.ts,.tsx ./src --fix > eslint-analyze.log || exit 0", "eslint-stat": "eslint-stat"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@musesscripts/yanxuan-b": "latest", "@types/jest": "24.0.13", "@types/node": "14.14.31", "@types/react": "16.8.19", "@types/react-dom": "16.8.4", "@types/react-router": "5.0.2", "@types/react-router-dom": "4.3.4", "@vscode-snippets/test-snippets": "0.0.6", "browserslist": "4.6.2", "cross-env": "^7.0.3", "http-proxy-middleware": "^2.0.1", "typescript": "^4.4.3", "postcss-preset-env": "7.8.1"}}