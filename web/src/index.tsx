import React from 'react';
import ReactDOM from 'react-dom';
import { axiosService } from '@sharkr/request';
import { AppConfig } from '@shark/core';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import { App } from './pages/app';
import { filterCode } from './utils';
import * as serviceWorker from './serviceWorker';

import './index.scss';

// 全局配置
AppConfig.configure({
  contextPath: process.env.CONTEXT_PATH,
  productCode: process.env.PRODUCT_CODE
});
// 设置code过滤
axiosService.setFilterCode(filterCode);

console.log('process.env.CONTEXT_PATH', process.env.CONTEXT_PATH);
console.log('process.env.PRODUCT_CODE', process.env.PRODUCT_CODE);
console.log('process.env.PROJECT_TYPE', process.env.PROJECT_TYPE);

export function mount(props?:any) {
  ReactDOM.render(
  <ConfigProvider locale={zhCN}>
      <App />
  </ConfigProvider>, 
  props.container);
  // If you want your app to work offline and load faster, you can change
  // unregister() to register() below. Note this comes with some pitfalls.
  // Learn more about service workers: https://bit.ly/CRA-PWA
  serviceWorker.unregister();
}

export function unmount(props:any) {
  ReactDOM.unmountComponentAtNode(props.container);
}

if (!(window as any).__POWERED_BY_MICRO__) {
  mount({container: document.getElementById('root')});
}
