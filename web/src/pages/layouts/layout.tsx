import React from 'react';
import { BaseProps } from '@sharkr/components';
import { BaseLayoutProps } from './interface';
import { BlankLayout } from './blankLayout';
import { BaseLayout } from '.';

export type LayoutProps = BaseLayoutProps & BaseProps;

export const Layout: React.FC<LayoutProps> = (props: LayoutProps) => {
  const isApp = process.env.PROJECT_TYPE === 'application';
  return <>{isApp ? <BaseLayout {...props} /> : <BlankLayout>{props?.children}</BlankLayout>}</>;
};
