import React from 'react';
import { Route, Redirect, Switch } from 'react-router-dom';
import { CorePage404, CorePage500 } from '@sharkr/components';
import { ItemList, ItemEdit, ItemDetail } from './item';

export const AppRoute: React.FC = () => (
  <Switch>
    <Route
      key="/item/list"
      path="/item/list"
      render={() => <ItemList />}
    />
    <Route
      key="/item/create"
      path="/item/create"
      render={() => <ItemDetail />}
    />
    <Route
      key="/item/detail"
      path="/item/detail"
      render={() => <ItemEdit />}
    />
    <Redirect exact from="/" to="/item/list" />
    <Route component={CorePage500} key="/500" path="/500" />
    <Route component={CorePage404} />
  </Switch>
);
