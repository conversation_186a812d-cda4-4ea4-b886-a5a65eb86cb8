import React, { useState, useEffect } from 'react';
import { Form, Button, Select, DatePicker, Row, Col, Table, message } from 'antd';
import {
  IDhxyCodeInfoListVO,
  ICodeInfoQueryParams,
  IEditionOption,
  IItemTypeOption
} from '../interface';
import { getCodeInfoList, getSkuAndEdition } from '../service';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Option } = Select;

export const SerialNumberList: React.FC<{}> = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<IDhxyCodeInfoListVO[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [editionOptions, setEditionOptions] = useState<IEditionOption[]>([]);
  const [itemTypeOptions, setItemTypeOptions] = useState<IItemTypeOption[]>([]);

  // 表格列定义
  const columns: any = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '活动编号',
      dataIndex: 'actNumber',
      key: 'actNumber',
      width: 120
    },
    {
      title: '版本',
      dataIndex: 'edition',
      key: 'edition',
      width: 100,
      render: (edition: number) => {
        const editionMap: { [key: number]: string } = {
          1: '经典版',
          2: '免费版'
        };
        return editionMap[edition] || edition;
      }
    },
    {
      title: '物品类型',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 120,
      render: (itemType: number) => {
        const itemTypeMap: { [key: number]: string } = {
          1: '分享版',
          2: '真爱版',
          3: '白金版',
          4: '典藏版'
        };
        return itemTypeMap[itemType] || itemType;
      }
    },
    {
      title: '激活码类型',
      dataIndex: 'codeType',
      key: 'codeType',
      width: 120,
      render: (codeType: number) => {
        const codeTypeMap: { [key: number]: string } = {
          1: '预约码',
          2: '激活码'
        };
        return codeTypeMap[codeType] || codeType;
      }
    },
    {
      title: '激活码总数',
      dataIndex: 'actCodeSum',
      key: 'actCodeSum',
      width: 120
    },
    {
      title: '预约码总数',
      dataIndex: 'resCodeSum',
      key: 'resCodeSum',
      width: 120
    },
    {
      title: '预约码已使用数',
      dataIndex: 'resCodeUsedSum',
      key: 'resCodeUsedSum',
      width: 140
    },
    {
      title: '预约码未使用数',
      dataIndex: 'resCodeNotUsedSum',
      key: 'resCodeNotUsedSum',
      width: 140
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (createTime: number) => {
        return createTime ? moment(createTime).format('YYYY-MM-DD HH:mm:ss') : '-';
      }
    }
  ];

  // 获取配置信息
  const fetchConfig = async () => {
    try {
      const response = await getSkuAndEdition();
      if (response.data) {
        setEditionOptions(response.data.editionList || []);
        setItemTypeOptions(response.data.itemList || []);
      }
    } catch (error) {
      console.error('获取配置信息失败:', error);
      message.error('获取配置信息失败');
    }
  };

  // 获取列表数据
  const fetchData = async (params?: ICodeInfoQueryParams) => {
    setLoading(true);
    try {
      const queryParams = {
        page: pagination.current,
        size: pagination.pageSize,
        ...params
      };
      
      const response = await getCodeInfoList(queryParams);
      if (response.data && response.data.result) {
        setDataSource(response.data.result || []);
        setPagination(prev => ({
          ...prev,
          total: response.data?.pagination?.total || 0
        }));
      }
    } catch (error) {
      console.error('获取列表数据失败:', error);
      message.error('获取列表数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 查询表单提交
  const onFinish = (values: any) => {
    const params: ICodeInfoQueryParams = {
      ...values,
      createTimeStart: values.createTimeRange?.[0]?.format('YYYY-MM-DD'),
      createTimeEnd: values.createTimeRange?.[1]?.format('YYYY-MM-DD')
    };
    delete params.createTimeRange;
    
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData(params);
  };

  // 重置表单
  const onReset = () => {
    form.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData();
  };

  // 表格分页变化
  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
    
    const formValues = form.getFieldsValue();
    const params: ICodeInfoQueryParams = {
      ...formValues,
      createTimeStart: formValues.createTimeRange?.[0]?.format('YYYY-MM-DD'),
      createTimeEnd: formValues.createTimeRange?.[1]?.format('YYYY-MM-DD'),
      page: paginationInfo.current,
      size: paginationInfo.pageSize
    };
    delete params.createTimeRange;
    
    fetchData(params);
  };

  useEffect(() => {
    fetchConfig();
    fetchData();
  }, []);

  return (
    <>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">激活序列号</span>
          <span className="sharkr-section-header-sub-title">（共{pagination.total}条）</span>
        </div>
        <div className="sharkr-section-content">
          <Form
            form={form}
            className="sharkr-form-inline margin-b-base"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            onFinish={onFinish}>
            <Row>
              <Col key="editions" span={8} xl={8} xxl={6}>
                <Form.Item label="版本" name="editions">
                  <Select 
                    className="sharkr-w-md" 
                    placeholder="请选择版本"
                    mode="multiple"
                    allowClear>
                    {editionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col key="itemTypes" span={8} xl={8} xxl={6}>
                <Form.Item label="物品类型" name="itemTypes">
                  <Select 
                    className="sharkr-w-md" 
                    placeholder="请选择物品类型"
                    mode="multiple"
                    allowClear>
                    {itemTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col key="createTimeRange" span={8} xl={8} xxl={6}>
                <Form.Item label="创建时间" name="createTimeRange">
                  <RangePicker className="sharkr-w-lg" />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col key="submit">
                <Button className="margin-r-base" type="primary" htmlType="submit">
                  查询
                </Button>
                <Button className="margin-r-base" onClick={onReset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <Table
            className="sharkr-table"
            columns={columns}
            dataSource={dataSource}
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
            }}
            onChange={handleTableChange}
            rowKey="id"
            scroll={{ x: 1200 }}
            style={{ marginBottom: -16 }}
          />
        </div>
      </section>
    </>
  );
};
