import React from 'react';
import { DownOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Typography, Row, Col, Table, Tooltip, Tabs, Dropdown, Menu } from 'antd';
import { PlainObject } from '@shark/core';

const { TabPane } = Tabs;
const { Paragraph } = Typography;

export const ItemDetail: React.FC<{}> = props => {
  const columns: any = [
    {
      title: '应用名称',
      width: 200,
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <a href="">{text}</a>
    },
    {
      title: '状态',
      width: 80,
      dataIndex: 'status',
      key: 'status'
    },
    {
      title: '创建时间',
      width: 120,
      dataIndex: 'time',
      key: 'time',
      defaultSortOrder: 'descend',
      sorter: (a: PlainObject, b: PlainObject) =>
        Number(new Date(a.time)) - Number(new Date(b.time)),
      sortDirections: ['descend', 'ascend']
    },
    {
      title: '更新时间',
      width: 120,
      dataIndex: 'time2',
      key: 'time2',
      sorter: (a: PlainObject, b: PlainObject) =>
        Number(new Date(a.time)) - Number(new Date(b.time)),
      sortDirections: ['descend', 'ascend']
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      render: (creator: string, record: PlainObject) => (
        <Paragraph ellipsis>{record && record.creator}</Paragraph>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (text: any, record: PlainObject) => (
        <span className="sharkr-table-actions">
          <Button className="action" type="link">
            操作1
          </Button>
          <Button className="action" type="link">
            操作2
          </Button>
          {record.key === '1' && (
            <Dropdown
              className="action"
              overlay={
                <Menu>
                  <Menu.Item>
                    <a href="http://www.alipay.com/" rel="noopener noreferrer" target="_blank">
                      操作3
                    </a>
                  </Menu.Item>
                  <Menu.Item>
                    <a href="http://www.taobao.com/" rel="noopener noreferrer" target="_blank">
                      操作4
                    </a>
                  </Menu.Item>
                  <Menu.Item>
                    <a href="http://www.tmall.com/" rel="noopener noreferrer" target="_blank">
                      操作5
                    </a>
                  </Menu.Item>
                </Menu>
              }>
              <Button type="link">更多</Button>
            </Dropdown>
          )}
          {record.key !== '1' && (
            <Button className="action" type="link">
              操作3
            </Button>
          )}
        </span>
      )
    }
  ];
  const data: PlainObject[] = [
    {
      key: '1',
      name: '供应链系统',
      status: '已上线',
      time: '2019-12-02 12:00:06',
      time2: '2019-12-02 12:00:06',
      creator: '王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨',
      tags: ['nice', 'developer']
    },
    {
      key: '2',
      name: '大促分析平台',
      status: '已上线',
      time: '2019-12-03 12:00:06',
      time2: '2019-12-03 12:00:06',
      creator: '王力可、罗毅杨',
      tags: ['loser']
    },
    {
      key: '3',
      name: '商品管理平台',
      status: '已上线',
      time: '2019-12-01 12:00:06',
      time2: '2019-12-01 12:00:06',
      creator: '孙佳',
      tags: ['cool', 'teacher']
    }
  ];

  const rowSelection = {
    onChange: () => {
      // do something
    }
  };
  return (
    <>
      <section className="sharkr-section-global" key="global">
        <div className="sharkr-section-global-header">
          <span className="sharkr-section-global-header-title">
            商品名称：星海·埃及长绒棉绣花浴巾
          </span>
          <div className="sharkr-tools">
            <Button className="tool">次操作</Button>
            <Button className="tool" type="primary">
              主操作
            </Button>
          </div>
        </div>
        <div className="sharkr-section-global-content">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <div className="text-base text-color-title">
                商品ID：
                <span className="text-color-base">3987172</span>
              </div>
            </Col>
            <Col span={24}>
              <div className="text-base text-color-title">
                品控类目：
                <span className="text-color-base">{'测试2 > 测试2-1 > 测试2-1-1 > 居家用品'}</span>
              </div>
            </Col>
          </Row>
        </div>
      </section>
      <Tabs className="sharkr-section-tabs" defaultActiveKey="1">
        <TabPane key="1" tab="商品概况">
          <section className="sharkr-section-secondary">
            <div className="sharkr-section-secondary-header">
              <span className="sharkr-section-secondary-header-title">1. 商品基本信息（带图）</span>
            </div>
            <div className="sharkr-section-secondary-content">
              <div className="bg-gray padding-8-2x">
                <Row>
                  <Col span={6}>
                    <img
                      alt="星海·埃及长绒棉绣花浴巾"
                      className="bg-white"
                      src="https://yanxuan-item.nosdn.127.net/a62370728e0fc0d2981f7c0e168b158a.png?imageView&thumbnail=352x352&quality=95"
                      style={{ height: 176 }}
                    />
                  </Col>
                  <Col span={18} style={{ marginBottom: -8 }}>
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          主站商品状态：
                          <span className="text-color-base">已下架</span>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          主站销售类目：
                          <span className="text-color-base">{'居家生活 > 床品件套'}</span>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          立项类型：
                          <span className="text-color-base">新品立项</span>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          商品归属：
                          <span className="text-color-base">全部</span>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          零售价：
                          <span className="text-color-base">119.00</span>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="text-base text-color-title">
                          商品组员：
                          <span className="text-color-base">孙悟空</span>
                        </div>
                      </Col>
                      <Col span={24}>
                        <div className="text-base text-color-title">
                          首次上架时间：
                          <span className="text-color-base">2015-12-29 11:57</span>
                        </div>
                      </Col>
                      <Col span={24}>
                        <div className="text-base text-color-title">
                          联系地址：
                          <span className="text-color-base">
                            中国杭州市滨江区网商路399号网易大厦二期小邮局很长很长很长
                          </span>
                        </div>
                      </Col>
                      <Col span={24}>
                        <div className="text-base text-color-title">
                          联系地址：
                          <span className="text-color-base">
                            中国杭州市滨江区网商路399号网易大厦二期小邮局很长很长很长
                          </span>
                        </div>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </div>
            </div>
          </section>
          <section className="sharkr-section-secondary">
            <div className="sharkr-section-secondary-header">
              <span className="sharkr-section-secondary-header-title">
                2. 商品基本信息（不带图）
              </span>
            </div>
            <div className="sharkr-section-secondary-content padding-t-base">
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    主站商品状态：
                    <span className="text-color-base">已下架</span>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    主站销售类目：
                    <span className="text-color-base">{'居家生活 > 床品件套'}</span>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    立项类型：
                    <span className="text-color-base">新品立项</span>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    商品归属：
                    <span className="text-color-base">全部</span>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    零售价：
                    <span className="text-color-base">119.00</span>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="text-base text-color-title">
                    商品组员：
                    <span className="text-color-base">孙悟空</span>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="text-base text-color-title">
                    首次上架时间：
                    <span className="text-color-base">2015-12-29 11:57</span>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="text-base text-color-title">
                    联系地址：
                    <span className="text-color-base">
                      中国杭州市滨江区网商路399号网易大厦二期小邮局很长很长很长
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
          </section>
          <section className="sharkr-section-secondary">
            <div className="sharkr-section-secondary-header">
              <span className="sharkr-section-secondary-header-title">3. 相关系统</span>
            </div>
            <div className="sharkr-section-secondary-content">
              <Table
                bordered
                className="sharkr-table"
                columns={columns}
                dataSource={data}
                pagination={false}
                rowSelection={rowSelection}
                tableLayout="fixed"
              />
            </div>
          </section>
          <section className="sharkr-section-secondary margin-b-0">
            <div className="sharkr-section-secondary-header">
              <span className="sharkr-section-secondary-header-title">4. 其他信息</span>
            </div>
            <div className="sharkr-section-secondary-content">
              <div className="bg-gray padding-8-2x">
                <div className="text-base margin-b-base">渠道供货价协作流程说明：</div>
                <div className="text-base text-color-secondary">
                  1、提交建议渠道供货价：
                  每月15日00:00:00-当月末最后一日23:59:59，商品管理部会提交下月的建议渠道供货价数据；
                </div>
                <div className="text-base text-color-secondary">
                  2、各BU加成系数提报渠道供货价：
                  商品管理部提交建议渠道供货价后，各BU部即可在本页面中操作【设置加成系数】和【提报渠道供货价】，最晚提报时间截止到当期月份的5号21:00:00，逾期视为该BU下的商品不提报渠道供货价，渠道不可对外报价售卖。
                </div>
                <div className="text-base text-color-secondary">
                  3、当各BU部商品都提报后，将实时生成渠道供货价；
                  如部分商品未在当月周期的5号21:00:00前提报，系统于当月周期的6号凌晨生成当月周期的渠道供货价。
                </div>
                <div className="text-base text-color-secondary">
                  4、逾期未提报渠道供货价的SKU，将对外不提供渠道供货价。
                </div>
              </div>
            </div>
          </section>
        </TabPane>
        <TabPane key="2" tab="其他tab">
          0 Content of Tab Pane 3
        </TabPane>
        <TabPane key="3" tab="Tab 3">
          0 Content of Tab Pane 3
        </TabPane>
      </Tabs>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <section className="sharkr-section">
            <div className="sharkr-section-header">
              <span className="sharkr-section-header-title">一级模块标题</span>
              <span className="sharkr-section-header-sub-title">辅助信息</span>
              <div className="sharkr-tools">
                <Button className="tool">刷新</Button>
              </div>
            </div>
            <div className="sharkr-section-content" style={{ height: 300 }}>
              <div className="paragraph-base" key="paragraph-base">
                本规范针对所有网易严选中后台，提供了：基础页面布局、组件、页面模板，以及快速搭建美观原型的提效小工具。旨在保证各系统交互视觉的统一，提升设计、开发、业务的工作效率。本规范针对所有网易严选中后台，提供了：基础页面布局、组件、页面模板，以及快速搭建美观原型的提效小工具。旨在保证各系统交互视觉的统一，提升设计、开发、业务的工作效率。本规范针对所有网易严选中后台，提供了：基础页面布局、组件、页面模板，以及快速搭建美观原型的提效小工具。旨在保证各系统交互视觉的统一，提升设计、开发、业务的工作效率。
                14px
              </div>
            </div>
          </section>
        </Col>
        <Col span={12}>
          <section className="sharkr-section">
            <div className="sharkr-section-header">
              <span className="sharkr-section-header-title">一级模块标题</span>
              <span className="sharkr-section-header-sub-title">辅助信息</span>
              <div className="sharkr-tools">
                <Button className="tool">刷新</Button>
              </div>
            </div>
            <div className="sharkr-section-content" style={{ height: 300 }}>
              <div className="paragraph-base" key="paragraph-base">
                区域高度保持一致
              </div>
            </div>
          </section>
        </Col>
        <Col span={8}>
          <section className="sharkr-section">
            <div className="sharkr-section-header">
              <span className="sharkr-section-header-title">一级模块标题</span>
              <span className="sharkr-section-header-sub-title">辅助信息</span>
              <div className="sharkr-tools">
                <Button className="tool">刷新</Button>
              </div>
            </div>
            <div className="sharkr-section-content" style={{ height: 200 }}>
              <div className="paragraph-base" key="paragraph-base">
                本规范针对所有网易严选中后台，提供了：基础页面布局、组件、页面模板，以及快速搭建美观原型的提效小工具。旨在保证各系统交互视觉的统一，提升设计、开发、业务的工作效率。本规范针对所有网易严选中后台，提供了：基础页面布局、组件、页面模板，以及快速搭建美观原型的提效小工具。旨在保证各系统交互视觉的统一，提升设计、开发、业务的工作效率。
                14px
              </div>
            </div>
          </section>
        </Col>
        <Col span={8}>
          <section className="sharkr-section">
            <div className="sharkr-section-header">
              <span className="sharkr-section-header-title">一级模块标题</span>
              <span className="sharkr-section-header-sub-title">辅助信息</span>
              <div className="sharkr-tools">
                <Button className="tool">刷新</Button>
              </div>
            </div>
            <div className="sharkr-section-content" style={{ height: 200 }}>
              <div className="paragraph-base" key="paragraph-base">
                区域高度保持一致
              </div>
            </div>
          </section>
        </Col>
        <Col span={8}>
          <section className="sharkr-section">
            <div className="sharkr-section-header">
              <span className="sharkr-section-header-title">一级模块标题</span>
              <span className="sharkr-section-header-sub-title">辅助信息</span>
              <div className="sharkr-tools">
                <Button className="tool">刷新</Button>
              </div>
            </div>
            <div className="sharkr-section-content" style={{ height: 200 }}>
              <div className="paragraph-base" key="paragraph-base">
                区域高度保持一致
              </div>
            </div>
          </section>
        </Col>
      </Row>
    </>
  );
};
