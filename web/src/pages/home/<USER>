import React, { Component } from 'react';
import { Button } from 'antd';

export class Home extends Component {
  render(): any {
    return (
      <>
        <section className="sharkr-section sharkr-section-fullscreen">
          <div className="sharkr-section-header">
            <span className="sharkr-section-header-title">全屏模块xx</span>
            <span className="sharkr-section-header-sub-title">辅助信息</span>
            <div className="sharkr-tools">
              <Button className="tool" type="primary">
                导出
              </Button>
              <Button className="tool">刷新</Button>
            </div>
          </div>
          <div className="sharkr-section-content">
            <div className="paragraph-base" key="paragraph-base">
              内容区域高度=模块最小高度 = 视窗高度 - 顶部高度 - 间距*2
            </div>
          </div>
        </section>
      </>
    );
  }
}
