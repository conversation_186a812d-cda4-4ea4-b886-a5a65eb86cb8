import React from 'react';
import { HashRouter as Router } from 'react-router-dom';
import { SharkRLayout } from '@sharkr/components';
import './app.scss';

const { Header, Container } = SharkRLayout;
const { Logo: Header<PERSON>ogo } = Header;

export const App: React.FC = () => (
  <Router>
    <SharkRLayout>
      <Header>
        <HeaderLogo desc="严选B端模板描述" product="严选B端模板" />
      </Header>
      <Container>
        <h1 className="text-center">我是登录页</h1>
      </Container>
    </SharkRLayout>
  </Router>
);
