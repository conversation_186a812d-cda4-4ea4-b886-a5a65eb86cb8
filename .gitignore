# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# ide
# /.vscode

# dependencies
/node_modules

# testing
/coverage

# production
/build
/dist

# package
/package

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# logs
npm-debug.log*
yarn-debug.log*
npm-error.log*
yarn-error.log*
web/eslint-analyze.log
web/eslint-analyze-file.log
web/eslint-analyze-function.log
web/test-code-temp